const path = require('path');
const fs = require('fs');

// 模拟 archiveManager 的路径解析逻辑
console.log('=== 测试 archiveManager 路径解析 ===');

// 导入7zip-bin来获取7z可执行文件路径
let sevenBin;
let sevenZipPath;

try {
  sevenBin = require("7zip-bin");
  sevenZipPath = sevenBin.path7za;
  
  // 验证7z可执行文件是否存在
  if (!fs.existsSync(sevenZipPath)) {
    throw new Error(`7z可执行文件不存在: ${sevenZipPath}`);
  }
  
  console.log(`📦 7zip-bin加载成功，路径: ${sevenZipPath}`);
} catch (error) {
  console.log(`❌ 7zip-bin加载失败:`, error.message);
  
  // 在打包环境中尝试备用路径
  const alternativePaths = [
    path.join(
      process.resourcesPath || '',
      "app.asar.unpacked",
      "node_modules",
      "7zip-bin",
      process.platform === "win32" ? "win" : process.platform,
      process.arch,
      "7za" + (process.platform === "win32" ? ".exe" : "")
    ),
    path.join(__dirname, "..", "..", "node_modules", "7zip-bin", process.platform === "win32" ? "win" : process.platform, process.arch, "7za" + (process.platform === "win32" ? ".exe" : "")),
    path.join(process.cwd(), "node_modules", "7zip-bin", process.platform === "win32" ? "win" : process.platform, process.arch, "7za" + (process.platform === "win32" ? ".exe" : ""))
  ];
  
  let found = false;
  
  for (const altPath of alternativePaths) {
    try {
      if (fs.existsSync(altPath)) {
        sevenZipPath = altPath;
        found = true;
        console.log(`📦 使用备用7z路径: ${sevenZipPath}`);
        break;
      }
    } catch (e) {
      // 继续尝试下一个路径
    }
  }
  
  if (!found) {
    throw new Error(`7zip-bin不可用且未找到备用路径: ${error.message}`);
  }
}

// 测试路径计算逻辑
function calculateRelativePaths(sourcePaths) {
  if (sourcePaths.length === 0) {
    throw new Error("源路径列表为空");
  }

  if (sourcePaths.length === 1) {
    const singlePath = sourcePaths[0];
    const parentDir = path.dirname(singlePath);
    const relativePath = path.basename(singlePath);
    console.log(`📦 单个路径处理: 工作目录=${parentDir}, 相对路径=${relativePath}`);
    return {
      workingDir: parentDir,
      relativePaths: [relativePath],
    };
  }

  // 多个路径的情况，找到真正的公共父目录
  const normalizedPaths = sourcePaths.map((p) => path.resolve(p));
  console.log(`📦 标准化路径:`, normalizedPaths.slice(0, 3), '...');

  // 找到公共父目录
  let commonParent = findTrueCommonParent(normalizedPaths);
  const relativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));

  // 验证相对路径的正确性
  const hasEmptyRelativePath = relativePaths.some((rp) => rp === "" || rp === ".");
  if (hasEmptyRelativePath) {
    console.log(`📦 检测到空相对路径，调整公共父目录`);
    commonParent = path.dirname(commonParent);
    const adjustedRelativePaths = normalizedPaths.map((p) => path.relative(commonParent, p));
    return {
      workingDir: commonParent,
      relativePaths: adjustedRelativePaths,
    };
  }

  return {
    workingDir: commonParent,
    relativePaths,
  };
}

function findTrueCommonParent(normalizedPaths) {
  if (normalizedPaths.length === 0) {
    throw new Error("路径列表为空");
  }

  if (normalizedPaths.length === 1) {
    return path.dirname(normalizedPaths[0]);
  }

  // 将所有路径分割成组件
  const pathComponents = normalizedPaths.map((p) => p.split(path.sep).filter(Boolean));
  const minLength = Math.min(...pathComponents.map((components) => components.length));

  // 找到公共前缀
  let commonLength = 0;
  for (let i = 0; i < minLength; i++) {
    const component = pathComponents[0][i];
    if (pathComponents.every((components) => components[i] === component)) {
      commonLength = i + 1;
    } else {
      break;
    }
  }

  if (commonLength === 0) {
    return path.sep;
  }

  const commonComponents = pathComponents[0].slice(0, commonLength);
  const commonParent = path.sep + path.join(...commonComponents);
  return commonParent;
}

// 测试文件路径
const testDir = '/tmp/test-upload/test-files';
const testFiles = [];

// 收集所有测试文件
function collectFiles(dir) {
  const items = fs.readdirSync(dir);
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    if (stat.isFile()) {
      testFiles.push(fullPath);
    } else if (stat.isDirectory()) {
      collectFiles(fullPath);
    }
  }
}

collectFiles(testDir);
console.log(`\n📊 收集到 ${testFiles.length} 个测试文件`);

// 测试路径计算
try {
  const { workingDir, relativePaths } = calculateRelativePaths(testFiles);
  console.log(`📦 工作目录: ${workingDir}`);
  console.log(`📦 相对路径数量: ${relativePaths.length}`);
  console.log(`📦 前3个相对路径: ${relativePaths.slice(0, 3).join(', ')}`);
  
  // 验证路径
  console.log('\n🔍 验证路径存在性:');
  let validCount = 0;
  let invalidCount = 0;
  
  for (let i = 0; i < Math.min(10, relativePaths.length); i++) {
    const relativePath = relativePaths[i];
    const fullPath = path.join(workingDir, relativePath);
    try {
      const stats = fs.statSync(fullPath);
      console.log(`   ✅ [${i}] "${relativePath}" -> ${stats.isFile() ? "文件" : "目录"}`);
      validCount++;
    } catch (error) {
      console.log(`   ❌ [${i}] "${relativePath}" -> 不存在或无法访问`);
      invalidCount++;
    }
  }
  
  console.log(`\n📊 路径验证结果: ${validCount} 个有效, ${invalidCount} 个无效`);
  
  if (invalidCount === 0) {
    console.log('✅ 所有测试路径验证通过，修复应该有效！');
  } else {
    console.log('❌ 存在无效路径，可能仍有问题');
  }
  
} catch (error) {
  console.log('❌ 路径计算失败:', error.message);
}

console.log('\n=== 测试完成 ===');
