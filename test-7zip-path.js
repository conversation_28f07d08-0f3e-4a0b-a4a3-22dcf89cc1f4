const path = require('path');
const fs = require('fs');

console.log('=== 7zip-bin 路径测试 ===');
console.log('process.platform:', process.platform);
console.log('process.arch:', process.arch);
console.log('process.resourcesPath:', process.resourcesPath);
console.log('__dirname:', __dirname);
console.log('process.cwd():', process.cwd());

// 测试原始 7zip-bin 加载
let sevenBin;
let sevenZipPath;

try {
  sevenBin = require("7zip-bin");
  sevenZipPath = sevenBin.path7za;
  
  console.log('\n=== 原始 7zip-bin 加载 ===');
  console.log('sevenBin.path7za:', sevenZipPath);
  
  // 验证文件是否存在
  if (fs.existsSync(sevenZipPath)) {
    console.log('✅ 7z可执行文件存在');
  } else {
    console.log('❌ 7z可执行文件不存在');
    throw new Error(`7z可执行文件不存在: ${sevenZipPath}`);
  }
} catch (error) {
  console.log('\n❌ 原始 7zip-bin 加载失败:', error.message);
  
  // 尝试备用路径
  console.log('\n=== 尝试备用路径 ===');
  const alternativePaths = [
    path.join(
      process.resourcesPath,
      "app.asar.unpacked",
      "node_modules",
      "7zip-bin",
      process.platform === "win32" ? "win" : process.platform,
      process.arch,
      "7za" + (process.platform === "win32" ? ".exe" : "")
    ),
    path.join(__dirname, "..", "..", "node_modules", "7zip-bin", process.platform === "win32" ? "win" : process.platform, process.arch, "7za" + (process.platform === "win32" ? ".exe" : "")),
    path.join(process.cwd(), "node_modules", "7zip-bin", process.platform === "win32" ? "win" : process.platform, process.arch, "7za" + (process.platform === "win32" ? ".exe" : ""))
  ];
  
  let found = false;
  
  for (let i = 0; i < alternativePaths.length; i++) {
    const altPath = alternativePaths[i];
    console.log(`尝试路径 ${i + 1}: ${altPath}`);
    
    try {
      if (fs.existsSync(altPath)) {
        sevenZipPath = altPath;
        found = true;
        console.log(`✅ 找到备用7z路径: ${sevenZipPath}`);
        break;
      } else {
        console.log(`❌ 路径不存在`);
      }
    } catch (e) {
      console.log(`❌ 路径检查失败:`, e.message);
    }
  }
  
  if (!found) {
    console.log('❌ 未找到任何可用的7z路径');
    process.exit(1);
  }
}

console.log('\n=== 最终结果 ===');
console.log('使用的7z路径:', sevenZipPath);

// 测试执行权限
try {
  fs.accessSync(sevenZipPath, fs.constants.F_OK | fs.constants.X_OK);
  console.log('✅ 7z可执行文件权限正常');
} catch (error) {
  console.log('❌ 7z可执行文件权限问题:', error.message);
}

console.log('\n=== 测试完成 ===');
