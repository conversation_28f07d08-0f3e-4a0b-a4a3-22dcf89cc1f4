const { initializeLogger, createLogger } = require('./electron/logger/index.ts');

console.log('=== 测试日志时区修复 ===');

// 初始化日志系统
initializeLogger({
  level: 'info',
  console: true,
  timestamp: true,
  moduleInfo: true
});

// 创建测试日志记录器
const testLogger = createLogger('timezone-test');

console.log('\n当前系统时间信息:');
const now = new Date();
console.log('本地时间:', now.toString());
console.log('UTC时间:', now.toUTCString());
console.log('ISO时间:', now.toISOString());

// 手动计算东8区时间
const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
console.log('东8区时间 (手动计算):', utc8Time.toISOString().replace('Z', '+08:00'));

console.log('\n=== 测试日志输出 ===');

// 测试不同级别的日志
testLogger.info('这是一条测试信息日志');
testLogger.warn('这是一条测试警告日志');
testLogger.error('这是一条测试错误日志');

console.log('\n请检查日志文件中的时间戳是否为东8区时间');
console.log('日志文件位置: ~/Library/Application Support/cloud-drive/logs/main.log');

console.log('\n=== 测试完成 ===');
