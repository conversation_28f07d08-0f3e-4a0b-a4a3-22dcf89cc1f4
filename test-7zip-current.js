const fs = require('fs');
const { spawn } = require('child_process');

console.log('=== 测试当前7zip-bin状态 ===');

try {
  const sevenBin = require("7zip-bin");
  const sevenZipPath = sevenBin.path7za;
  
  console.log('7zip-bin路径:', sevenZipPath);
  console.log('文件是否存在 (fs.existsSync):', fs.existsSync(sevenZipPath));
  
  // 测试spawn是否能执行
  console.log('\n=== 测试spawn执行 ===');
  
  const testProcess = spawn(sevenZipPath, ['--help'], { stdio: 'pipe' });
  
  testProcess.on('error', (error) => {
    console.log('❌ spawn错误:', error.message);
  });
  
  testProcess.on('close', (code) => {
    console.log('✅ spawn成功，退出码:', code);
  });
  
  testProcess.stdout.on('data', (data) => {
    console.log('✅ spawn输出:', data.toString().substring(0, 100) + '...');
  });
  
  // 5秒后结束测试
  setTimeout(() => {
    testProcess.kill();
    console.log('\n=== 测试完成 ===');
    process.exit(0);
  }, 5000);
  
} catch (error) {
  console.log('❌ require("7zip-bin")失败:', error.message);
}
